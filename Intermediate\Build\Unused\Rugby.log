﻿  *** Found installed NintendoSDK version 19.3.5.  That version is greater than the maximum suggested version of 16.2.6 and is unverified. ***
  Building RugbyEditor and ShaderCompileWorker...
  Using Visual Studio 2019 14.29.30159 toolchain (C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133) and Windows 10.0.22000.0 SDK (C:\Program Files (x86)\Windows Kits\10).
  [Upgrade]
  [Upgrade] Using backward-compatible build settings. The latest version of UE4 sets the following values by default, which may require code changes:
  [Upgrade]     bLegacyPublicIncludePaths = false                 => Omits subfolders from public include paths to reduce compiler command line length. (Previously: true).
  [Upgrade]     ShadowVariableWarningLevel = WarningLevel.Error   => Treats shadowed variable warnings as errors. (Previously: WarningLevel.Warning).
  [Upgrade]     PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs   => Set in build.cs files to enables IWYU-style PCH model. See https://docs.unrealengine.com/en-US/Programming/BuildTools/UnrealBuildTool/IWYU/index.html. (Previously: PCHUsageMode.UseSharedPCHs).
  [Upgrade] Suppress this message by setting 'DefaultBuildSettings = BuildSettingsVersion.V2;' in RugbyEditor.Target.cs, and explicitly overriding settings that differ from the new defaults.
  [Upgrade]
  Building 29 actions with 24 processes...
    [1/29] Module.Rugby.gen.10_of_14.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime/Slate/Public/Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
    [2/29] Module.Rugby.18_of_50.cpp
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Match/HeadCaptureActor.cpp(138): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HeadCaptureActor.cpp(272): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/Marking/RULineoutIndicator.cpp(38): warning C4305: 'argument': truncation from 'double' to 'float'
    [3/29] Module.Rugby.17_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Match/Cutscenes/SSCutSceneSequencing.cpp(3742): warning C4456: declaration of 'teamName' hides previous local declaration
    W:/NRL/Source/Rugby/Match/Cutscenes/SSCutSceneSequencing.cpp(3728): note: see declaration of 'teamName'
    [4/29] Module.Rugby.12_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:\NRL\Source\Rugby\Match\AI\Roles\Competitors\RURoleStandardBallHolder.cpp(314): warning C4701: potentially uninitialized local variable 'kickType' used
    [5/29] Module.Rugby.13_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    [6/29] Module.Rugby.32_of_50.cpp
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    [7/29] Module.Rugby.28_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Match/RugbyUnion/RUGameSettings.cpp(124): warning C4355: 'this': used in base member initializer list
    [8/29] Module.Rugby.41_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:/NRL/Source/Rugby/UI/Populators/WWUIPopulatorHUBCurrentProGoals.cpp(142): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Populators/WWUIPopulatorInGameCurrentProGoals.cpp(240): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    [9/29] Module.Rugby.30_of_50.cpp
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(722): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(735): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(749): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(762): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(776): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(789): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(812): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(824): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(836): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(859): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(871): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(883): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    [10/29] Module.Rugby.gen.12_of_14.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    [11/29] Module.Rugby.24_of_50.cpp
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    C:\Program Files (x86)\Windows Kits\10\include\10.0.22000.0\um\dinput.h: DIRECTINPUT_VERSION undefined. Defaulting to version 0x0800
W:/NRL/Source/Rugby/Match/RugbyUnion/CompetitionMode/RUCareerModeManager.cpp(3609): warning C4305: 'argument': truncation from 'double' to 'float'
    [12/29] Module.Rugby.10_of_50.cpp
W:/NRL/Source/Rugby/Mab/XDS/XDSXMLParser.cpp(747): warning C4996: 'strcpy': This function or variable may be unsafe. Consider using strcpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/XDS/XDSXMLParser.cpp(851): warning C4996: 'strncpy': This function or variable may be unsafe. Consider using strncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/XDS/XDSXMLParser.cpp(876): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/XDS/XDSXMLParser.cpp(891): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/XDS/XDSXMLParser.cpp(913): warning C4996: 'strncpy': This function or variable may be unsafe. Consider using strncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/XDS/XDSXMLParser.cpp(1095): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/XDS/XDSXMLParser.cpp(1112): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/XDS/XDSXMLParser.cpp(1132): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:/NRL/Source/Rugby/Match/AI/Actions/RUActionFend.cpp(396): warning C4305: 'initializing': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    [13/29] Module.Rugby.4_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    [14/29] Module.Rugby.14_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Match/AI/SetPlays/SSSetPlayManager.cpp(279): warning C4305: 'argument': truncation from 'double' to 'float'
W:/NRL/Source/Rugby/Match/AI/SetPlays/SSSetPlayManager.cpp(297): warning C4305: 'argument': truncation from 'double' to 'float'
    [15/29] Module.Rugby.40_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime/Slate/Public/Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/UI/Components/WWUIUserWidgetPlayerDraftPanel.cpp(426): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Components/WWUIUserWidgetPlayerDraftPanel.cpp(451): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Components/WWUIUserWidgetPlayerDraftPanel.cpp(835): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Components/WWUIUserWidgetVoipList.cpp(328): warning C4996: 'APlayerState::Ping': This member will be made private. Use GetPing or SetPing instead. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Components/WWUIUserWidgetVoipList.cpp(333): warning C4996: 'APlayerState::PlayerId': This member will be made private. Use GetPlayerId or SetPlayerId instead. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/UI/Populators/WWUICareerMainMenuListboxPopulator.cpp(290): warning C4996: 'UWidget::SetNavigationRule': SetNavigationRule is deprecated. Please use either SetNavigationRuleBase or SetNavigationRuleExplicit or SetNavigationRuleCustom or SetNavigationRuleCustomBoundary. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Populators/WWUICareerMainMenuListboxPopulator.cpp(291): warning C4996: 'UWidget::SetNavigationRule': SetNavigationRule is deprecated. Please use either SetNavigationRuleBase or SetNavigationRuleExplicit or SetNavigationRuleCustom or SetNavigationRuleCustomBoundary. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Populators/WWUICompetitionMainMenuListboxPopulator.cpp(392): warning C4996: 'UWidget::SetNavigationRule': SetNavigationRule is deprecated. Please use either SetNavigationRuleBase or SetNavigationRuleExplicit or SetNavigationRuleCustom or SetNavigationRuleCustomBoundary. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Populators/WWUICompetitionMainMenuListboxPopulator.cpp(393): warning C4996: 'UWidget::SetNavigationRule': SetNavigationRule is deprecated. Please use either SetNavigationRuleBase or SetNavigationRuleExplicit or SetNavigationRuleCustom or SetNavigationRuleCustomBoundary. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Populators/WWUIPopulatorCareerPlayerDraft.cpp(631): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Populators/WWUIPopulatorCareerRecruit.cpp(182): warning C4996: 'strncpy': This function or variable may be unsafe. Consider using strncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/UI/Populators/WWUIPopulatorCareerRecruit.cpp(184): warning C4996: 'strncpy': This function or variable may be unsafe. Consider using strncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
    [16/29] Module.Rugby.29_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    [17/29] Module.Rugby.26_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Match/RugbyUnion/PhaseHandlers/RUGamePhaseRuck.cpp(2739): warning C4946: reinterpret_cast used between related classes: 'SSTeam' and 'RUTeam'
    W:\NRL\Source\Rugby\Match/RugbyUnion/RULineoutRater.h(23): note: see declaration of 'SSTeam'
    W:\NRL\Source\Rugby\Match/SSReplaysMk2/SSReplayManager.h(34): note: see declaration of 'RUTeam'
    [18/29] Module.Rugby.31_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:\NRL\Plugins\wwStadiumTool\Source\wwStadiumRuntime\Public\SkyDomeBase.h(466): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUTeamFacesGenerator.cpp(1041): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUTeamFacesGenerator.cpp(1171): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    [19/29] Module.Rugby.46_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime/Slate/Public/Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(192): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(192): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(192): warning C4996: 'FindField': FindField will no longer return properties. Use FindFProperty instead or FindUField if you want to find functions or enums. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(660): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(660): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(660): warning C4996: 'FindField': FindField will no longer return properties. Use FindFProperty instead or FindUField if you want to find functions or enums. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(786): warning C4996: UObjectProperty has been renamed to FObjectProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(786): warning C4996: UObjectProperty has been renamed to FObjectProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(786): warning C4996: 'FindField': FindField will no longer return properties. Use FindFProperty instead or FindUField if you want to find functions or enums. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(823): warning C4996: 'FSlateApplication::FindWidgetWindow': The FindWidgetWindow method that takes an FWidgetPath has been deprecated.  If you dont need the widget path, use FindWidgetWindow(MyWidget) instead.  If you need the path use GeneratePathToWidget Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(1011): warning C4456: declaration of 'clickedFieldParent' hides previous local declaration
    W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(998): note: see declaration of 'clickedFieldParent'
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(1024): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(1024): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(1024): warning C4996: 'FindField': FindField will no longer return properties. Use FindFProperty instead or FindUField if you want to find functions or enums. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(1444): warning C4456: declaration of 'clickedFieldParent' hides previous local declaration
    W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(1431): note: see declaration of 'clickedFieldParent'
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(1456): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(1456): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMainMenu.cpp(1456): warning C4996: 'FindField': FindField will no longer return properties. Use FindFProperty instead or FindUField if you want to find functions or enums. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenMatchRulingsSettings.cpp(100): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    [20/29] Module.Rugby.16_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Match/Cutscenes/SSCutSceneManager.cpp(1958): warning C4996: 'UMovieSceneSequencePlayer::JumpToSeconds': JumpToSeconds is deprecated, use SetPlaybackPosition. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    [21/29] Module.Rugby.38_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Editor/PropertyEditor/Public/DetailWidgetRow.h(98): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Editor/PropertyEditor/Public/DetailWidgetRow.h(99): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Editor/PropertyEditor/Public/DetailWidgetRow.h(100): warning C4355: 'this': used in base member initializer list
W:\NRL\Plugins\wwStadiumTool\Source\wwStadiumRuntime\Public\SkyDomeBase.h(466): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:\Engine\Engine\Source\ThirdParty\Steamworks\Steamv146\sdk\public\steam\matchmakingtypes.h(45): warning C4996: 'strncpy': This function or variable may be unsafe. Consider using strncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:\Engine\Engine\Source\ThirdParty\Steamworks\Steamv146\sdk\public\steam\matchmakingtypes.h(47): warning C4996: 'strncpy': This function or variable may be unsafe. Consider using strncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:\Engine\Engine\Source\ThirdParty\Steamworks\Steamv146\sdk\public\steam\matchmakingtypes.h(161): warning C4996: '_snprintf': This function or variable may be unsafe. Consider using _snprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:\Engine\Engine\Source\ThirdParty\Steamworks\Steamv146\sdk\public\steam\matchmakingtypes.h(246): warning C4996: 'strncpy': This function or variable may be unsafe. Consider using strncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:\NRL\Source\Rugby\UI/WWUISteamOverlayTracker.h(17): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/RugbyGameInstance.cpp(2672): warning C4996: 'APlayerState::PlayerId': This member will be made private. Use GetPlayerId or SetPlayerId instead. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/RugbyGameInstance.cpp(2674): warning C4996: 'APlayerState::UniqueId': This member will be made private. Use GetUniqueId or SetUniqueId instead. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/RugbyGameInstance.cpp(3340): warning C4996: 'FSlateApplication::ForEachUser': ForEachUser now provides an FSlateUser& parameter to the lambda instead of an FSlateUser* Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/RugbyGameInstance.cpp(14180): warning C4996: 'getenv': This function or variable may be unsafe. Consider using _dupenv_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/RugbyGameInstance.cpp(14181): warning C4996: 'getenv': This function or variable may be unsafe. Consider using _dupenv_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:\NRL\Source\Rugby\RugbyGameInstance.cpp(14185): warning C4701: potentially uninitialized local variable 'hostX' used
    [22/29] Module.Rugby.45_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenCustomisePlayerDebug.cpp(919): warning C4996: 'getenv': This function or variable may be unsafe. Consider using _dupenv_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenCustomiseSelectKit.cpp(415): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenCustomiseSelectTeam.cpp(1023): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenFulltimeTeamStatsWindow.cpp(106): warning C4305: 'argument': truncation from 'double' to 'float'
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenFulltimeWindow.cpp(47): warning C4305: 'argument': truncation from 'double' to 'float'
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenGraphicsSettings.cpp(60): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenGraphicsSettings.cpp(172): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenInGameInjury.cpp(215): warning C4996: UObjectProperty has been renamed to FObjectProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenInGameInjury.cpp(215): warning C4996: UObjectProperty has been renamed to FObjectProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenInGameInjury.cpp(215): warning C4996: 'FindField': FindField will no longer return properties. Use FindFProperty instead or FindUField if you want to find functions or enums. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenInGameInjury.cpp(252): warning C4996: 'FSlateApplication::FindWidgetWindow': The FindWidgetWindow method that takes an FWidgetPath has been deprecated.  If you dont need the widget path, use FindWidgetWindow(MyWidget) instead.  If you need the path use GeneratePathToWidget Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenListPlayers.cpp(1778): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenListTeams.cpp(1960): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    [23/29] Module.Rugby.34_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Editor/PropertyEditor/Public/DetailWidgetRow.h(98): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Editor/PropertyEditor/Public/DetailWidgetRow.h(99): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Editor/PropertyEditor/Public/DetailWidgetRow.h(100): warning C4355: 'this': used in base member initializer list
W:\NRL\Plugins\wwStadiumTool\Source\wwStadiumRuntime/Public/SkyDomeBase.h(466): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/SIFGameWorld.cpp(998): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/SIFGameWorld.cpp(1978): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/SIFGameWorld.cpp(3786): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/SIFGameWorld.cpp(5211): warning C4996: 'APlayerState::PlayerId': This member will be made private. Use GetPlayerId or SetPlayerId instead. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/SIFGameWorld.cpp(6766): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/SIFGameWorld.cpp(6803): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    [24/29] Module.Rugby.42_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:/NRL/Source/Rugby/UI/Populators/WWUIPopulatorTrainingRolodex.cpp(99): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Populators/WWUIPopulatorTrainingRolodex.cpp(117): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Populators/WWUIProModeMainMenuListboxPopulator.cpp(292): warning C4996: 'UWidget::SetNavigationRule': SetNavigationRule is deprecated. Please use either SetNavigationRuleBase or SetNavigationRuleExplicit or SetNavigationRuleCustom or SetNavigationRuleCustomBoundary. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Populators/WWUIProModeMainMenuListboxPopulator.cpp(293): warning C4996: 'UWidget::SetNavigationRule': SetNavigationRule is deprecated. Please use either SetNavigationRuleBase or SetNavigationRuleExplicit or SetNavigationRuleCustom or SetNavigationRuleCustomBoundary. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/Modals/WWUIModalSwitchFriendPlay.cpp(91): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/UI/Screens/Modals/WWUIModalWarningLobbyTimeout.cpp(77): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenCameraAndVisualsSettings.cpp(57): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenCameraAndVisualsSettings.cpp(652): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    [25/29] Module.Rugby.35_of_50.cpp
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
    [26/29] Module.Rugby.39_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:/NRL/Source/Rugby/RugbyWorldSettings.cpp(7): warning C4996: 'AWorldSettings::Pauser': This property is deprecated. Please use Get/SetPauserPlayerState(). Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/RUUIDatabaseQueryManager.cpp(361): warning C4996: 'strncpy': This function or variable may be unsafe. Consider using strncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/SIFNetworkNonGameMessageHandler.cpp(675): warning C4996: 'strncpy': This function or variable may be unsafe. Consider using strncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
    [27/29] UE4Editor-Rugby.lib
       Creating library W:\NRL\Intermediate\Build\Win64\UE4Editor\Development\Rugby\UE4Editor-Rugby.lib and object W:\NRL\Intermediate\Build\Win64\UE4Editor\Development\Rugby\UE4Editor-Rugby.exp
    [28/29] UE4Editor-Rugby.dll
       Creating library W:\NRL\Intermediate\Build\Win64\UE4Editor\Development\Rugby\UE4Editor-Rugby.suppressed.lib and object W:\NRL\Intermediate\Build\Win64\UE4Editor\Development\Rugby\UE4Editor-Rugby.suppressed.exp
    [29/29] RugbyEditor.target
  Total time in Parallel executor: 132.93 seconds
  Total execution time: 135.39 seconds
